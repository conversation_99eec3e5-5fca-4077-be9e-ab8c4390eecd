# Trusty TEE GP Panic 功能设计方案

## 1. 概述

### 1.1 设计目标
设计并实现 GP TEE 标准的 `TEE_Panic(TEE_Result panicCode)` 功能，利用现有 Trusty 系统调用和 `rctee_app_exit_etc` 函数实现错误消息传递和程序终止。

### 1.2 设计原则
- **最小修改原则**：最大化复用现有 Trusty 基础设施
- **GP 标准兼容**：严格遵循 GP Internal Core API 规范
- **错误传递机制**：确保 panic 错误码能够传递给对端
- **系统稳定性**：保证 panic 后系统能够正确清理和终止

## 2. 整体架构设计

### 2.1 调用流程

```
TEE_Panic(panicCode) 
    ↓
用户空间系统调用
    ↓
内核 rctee_app_exit_etc(EXIT_FAILURE, panicCode, true)
    ↓
错误消息通知对端 + 应用清理 + panic("\n")
```

### 2.2 三层架构

1. **GP API 层**：`TEE_Panic` 函数实现
2. **系统调用层**：新增 panic 专用系统调用
3. **内核处理层**：扩展 `rctee_app_exit_etc` 支持 panic 场景

## 3. 详细设计方案

### 3.1 GP API 层设计

#### 3.1.1 函数接口
```c
void TEE_Panic(TEE_Result panicCode);
```

#### 3.1.2 实现策略
- **位置**：`user/base/lib/libutee/tee_panic.c`
- **功能**：参数验证 + 系统调用封装
- **特点**：`__NO_RETURN` 属性，确保函数不返回

#### 3.1.3 实现要点
1. 验证 `panicCode` 参数有效性
2. 调用专用系统调用 `_rctee_panic`
3. 添加备用终止机制（防止系统调用失败）

### 3.2 系统调用层设计

#### 3.2.1 新增系统调用
- **系统调用名**：`_rctee_panic`
- **参数**：`uint32_t panic_code`
- **返回值**：无（不返回）

#### 3.2.2 系统调用编号分配
- 在现有系统调用编号基础上分配新编号
- 更新用户空间和内核空间的系统调用表

#### 3.2.3 实现位置
- **用户空间**：`user/base/lib/libc-trusty/rctee_syscalls.h`
- **内核空间**：`kernel/rctee/lib/rctee/rctee_core/rctee_syscalls.c`

### 3.3 内核处理层设计

#### 3.3.1 系统调用处理函数
```c
long sys_panic(uint32_t panic_code);
```

#### 3.3.2 处理流程
1. **参数转换**：将 `TEE_Result` 转换为内核错误码格式
2. **调用退出函数**：`rctee_app_exit_etc(EXIT_FAILURE, panic_code, true)`
3. **确保不返回**：函数标记为 `__NO_RETURN`

#### 3.3.3 rctee_app_exit_etc 增强
- **现有功能保持不变**：应用清理、通知机制、状态管理
- **panic 特殊处理**：在最后添加 `panic("\n")` 调用
- **错误码传递**：确保 `panic_code` 能够通过 crash notifier 传递给对端

## 4. 错误消息传递机制

### 4.1 现有通知机制利用
- **crash notifier**：利用现有的 `notifier->crash(app, crash_reason)` 机制
- **错误码传递**：`panic_code` 作为 `crash_reason` 参数传递
- **对端通知**：通过 TIPC 或其他 IPC 机制通知对端应用

### 4.2 错误码格式
- **输入格式**：GP 标准 `TEE_Result` (32位)
- **传递格式**：直接作为 `uint32_t crash_reason` 传递
- **标识机制**：可通过特定位模式识别为 panic 错误码

### 4.3 对端处理
- **错误接收**：对端通过现有错误通知机制接收
- **错误识别**：根据错误码格式识别为 TEE_Panic 错误
- **错误处理**：对端应用根据 GP 规范处理 panic 事件

## 5. 系统终止机制

### 5.1 终止流程
1. **应用清理**：释放资源、关闭连接、清理状态
2. **通知发送**：向对端发送 crash 通知
3. **最终终止**：调用 `panic("\n")` 确保系统级终止

### 5.2 panic 调用位置
- **位置**：`rctee_app_exit_etc` 函数末尾
- **条件**：当 `report_crash` 为 true 且来源为 TEE_Panic 时
- **格式**：`panic("TEE_Panic: 0x%08x\n", crash_reason)`

### 5.3 系统稳定性保证
- **资源清理**：确保所有应用资源得到正确释放
- **状态一致性**：保证应用状态转换的原子性
- **系统恢复**：支持应用重启机制（如果配置允许）

## 6. 实现要点

### 6.1 参数验证
- **TEE_Result 验证**：检查是否为有效的 GP 错误码
- **范围检查**：确保错误码在合理范围内
- **特殊值处理**：对 TEE_SUCCESS 等特殊值的处理策略

### 6.2 线程安全
- **系统调用原子性**：确保 panic 系统调用的原子执行
- **资源竞争避免**：防止与其他退出路径的资源竞争
- **锁机制**：复用现有的应用管理锁机制

### 6.3 错误处理
- **系统调用失败**：备用终止机制（直接调用 abort）
- **通知失败**：即使通知失败也要确保应用终止
- **异常情况**：处理各种异常情况下的安全终止

## 7. 兼容性考虑

### 7.1 GP 标准兼容
- **接口兼容**：严格遵循 GP Internal Core API 规范
- **行为兼容**：panic 行为符合 GP 标准要求
- **错误码兼容**：支持所有标准 GP 错误码

### 7.2 Trusty 系统兼容
- **现有机制复用**：最大化利用现有退出和通知机制
- **系统调用兼容**：与现有系统调用体系保持一致
- **应用管理兼容**：与现有应用生命周期管理兼容

### 7.3 向后兼容
- **现有应用**：不影响现有应用的正常运行
- **API 扩展**：作为新增 API，不破坏现有接口
- **系统升级**：支持平滑的系统升级路径

## 8. 测试策略

### 8.1 单元测试
- **TEE_Panic 函数测试**：验证各种错误码的处理
- **系统调用测试**：验证系统调用的正确性
- **错误传递测试**：验证错误码的正确传递

### 8.2 集成测试
- **端到端测试**：验证完整的 panic 流程
- **对端通知测试**：验证对端能够正确接收错误通知
- **系统稳定性测试**：验证 panic 后系统的稳定性

### 8.3 压力测试
- **并发 panic 测试**：多个应用同时 panic 的处理
- **资源耗尽测试**：在资源紧张情况下的 panic 处理
- **异常场景测试**：各种异常情况下的 panic 行为

## 9. 总结

### 9.1 方案优势
1. **最小侵入性**：充分利用现有 Trusty 基础设施
2. **标准兼容性**：严格遵循 GP TEE 标准规范
3. **系统稳定性**：确保 panic 后系统的正确清理和终止
4. **错误传递**：可靠的错误码传递机制
5. **实现简单**：设计清晰，实现复杂度低

### 9.2 关键特性
- **GP 标准 TEE_Panic 接口**：完整实现 GP 规范要求
- **系统调用机制**：利用 Trusty 系统调用进入内核
- **错误消息传递**：通过现有 crash notifier 机制传递错误
- **可靠终止机制**：确保应用和系统的正确终止
- **向后兼容性**：不影响现有系统功能

### 9.3 实现路径
1. **第一阶段**：实现基础 TEE_Panic 函数和系统调用
2. **第二阶段**：增强 rctee_app_exit_etc 支持 panic 场景
3. **第三阶段**：完善错误传递和测试验证
4. **第四阶段**：性能优化和文档完善
