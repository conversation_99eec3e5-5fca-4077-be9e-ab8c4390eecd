# Trusty TEE 属性API内部接口设计文档

## 1. 概述

本文档整理Trusty TEE属性API设计中除GP标准接口外的所有内部接口，包括属性管理层、TIPC服务层和辅助函数的详细接口规范。

## 2. 属性管理层接口

### 2.1 核心属性获取接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result propget_get_property(TEE_PropSetHandle h, const char* name, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | h: 属性集句柄，可以是伪句柄(TEE_PROPSET_*)或枚举器句柄<br>name: 属性名称字符串指针<br>type: 指向属性类型枚举的指针，用于输入期望类型和输出实际类型<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针，输入缓冲区大小，输出实际长度 |
| 输出 | type: 返回实际的属性类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性<br>TEE_ERROR_ITEM_NOT_FOUND: 属性未找到<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_BAD_PARAMETERS: 参数无效 |
| 说明 | 统一属性获取入口，实现本地优先、远程兜底的智能路由机制 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result propset_get(TEE_PropSetHandle h, const struct user_ta_property** eps, size_t* eps_len)` |
| 输入 | h: 属性集句柄(TEE_PROPSET_CURRENT_TA、TEE_PROPSET_CURRENT_CLIENT、TEE_PROPSET_TEE_IMPLEMENTATION) |
| 输出 | eps: 返回指向属性数组的指针<br>eps_len: 返回属性数组的长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性集<br>TEE_ERROR_ITEM_NOT_FOUND: 无效的属性集句柄 |
| 说明 | 根据属性集句柄获取对应的本地属性数组，参考OP-TEE的propset_get函数 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result propget_get_ext_prop(const struct user_ta_property* ep, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | ep: 指向属性结构体的指针<br>type: 指向属性类型枚举的指针<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回属性的实际类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功提取属性值<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_GENERIC: 不支持的属性类型 |
| 说明 | 从属性结构体中提取属性值，支持BOOL、U32、U64、STRING类型的属性 |

### 2.2 枚举器管理接口（参考OP-TEE）

基于OP-TEE的简洁枚举器设计，枚举器直接使用 `TEE_PropSetHandle` 类型，内部为 `struct prop_enumerator` 结构。

**当前实现状态**：
- ✅ 已实现：基础属性获取API（4个）
- 🔄 规划中：属性枚举器API（6个）
- 📋 预留：扩展属性类型支持

## 3. TIPC服务层接口

### 3.1 属性查找服务接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_property_from_tipc_service(TEE_PropSetHandle prop_set, const char* name, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | prop_set: 属性集句柄<br>name: 属性名称字符串<br>type: 指向期望属性类型的指针<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回实际的属性类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_ITEM_NOT_FOUND: 属性未找到<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足 |
| 说明 | 通过TIPC服务从内核空间获取属性值，处理连接建立、消息收发和响应解析 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_property_by_index_from_tipc_service(TEE_PropSetHandle prop_set, uint32_t index, char* name, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | prop_set: 属性集句柄<br>index: 属性索引(相对于远程属性的起始位置)<br>name: 属性名称缓冲区指针<br>type: 指向属性类型的指针<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | name: 返回属性名称<br>type: 返回属性类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_ITEM_NOT_FOUND: 索引超出范围<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足 |
| 说明 | 通过索引从TIPC服务获取属性信息，主要用于枚举器的远程属性访问（预留接口，当前实现暂不支持） |

### 3.2 属性枚举服务接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_property_name_from_tipc_service(TEE_PropSetHandle prop_set, uint32_t index, void* nameBuffer, size_t* nameBufferLen)` |
| 输入 | prop_set: 属性集句柄<br>index: 属性索引(相对于远程属性的起始位置)<br>nameBuffer: 属性名称缓冲区指针<br>nameBufferLen: 指向缓冲区长度的指针 |
| 输出 | nameBuffer: 填充属性名称<br>nameBufferLen: 返回实际的名称长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性名称<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_ITEM_NOT_FOUND: 索引超出范围<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足 |
| 说明 | 从TIPC服务获取指定索引的属性名称，用于枚举器的TEE_GetPropertyName实现（预留接口，当前实现暂不支持） |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result check_tipc_property_exists(TEE_PropSetHandle prop_set, uint32_t index)` |
| 输入 | prop_set: 属性集句柄<br>index: 属性索引(相对于远程属性的起始位置) |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS: 指定索引的属性存在<br>TEE_ERROR_ITEM_NOT_FOUND: 指定索引的属性不存在或索引超出范围<br>TEE_ERROR_COMMUNICATION: TIPC通信失败 |
| 说明 | 检查TIPC服务中是否存在指定索引的属性，用于枚举器的TEE_GetNextProperty实现（预留接口，当前实现暂不支持） |

## 4. 辅助函数接口

### 4.1 属性值处理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result copy_property_value_to_buffer(const struct property_response* resp, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | resp: 指向TIPC响应消息的指针<br>type: 指向属性类型的指针<br>buf: 目标缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回属性的实际类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功复制属性值<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_BAD_FORMAT: 属性类型不匹配 |
| 说明 | 从TIPC响应消息中提取属性值并复制到用户缓冲区，处理类型检查和长度验证 |

### 4.2 句柄检查接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static bool is_propset_pseudo_handle(TEE_PropSetHandle h)` |
| 输入 | h: 属性集句柄 |
| 输出 | 无 |
| 返回值 | true: 是伪句柄(TEE_PROPSET_*)<br>false: 不是伪句柄(可能是枚举器句柄) |
| 说明 | 检查句柄是否为预定义的伪句柄，用于区分属性集句柄和枚举器句柄 |

## 5. 数据结构定义

### 5.1 枚举器结构

```c
struct prop_enumerator {
    uint32_t idx;                        /* 当前枚举索引 */
    TEE_PropSetHandle prop_set;          /* 关联的属性集句柄 */
};
```

### 5.2 TIPC通信结构（当前实现）

```c
/* 当前实现的属性请求结构 */
struct property_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    char name[64];                       /* 属性名称 */
};

/* 当前实现的属性响应结构 */
struct property_response {
    int32_t result;                      /* 操作结果 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
    };
};
```

**说明**：当前实现采用简化的TIPC协议，专注于基本的属性获取功能。

## 6. 常量定义

```c
#define PROP_ENUMERATOR_NOT_STARTED     0xffffffff
#define GENERIC_TA_PORT                 "com.android.trusty.generic_ta"

/* 当前实现的TIPC命令 */
enum generic_ta_cmd {
    GENERIC_TA_CMD_INVALID = 0,
    GENERIC_TA_CMD_GET_PROPERTY_STRING = 1,
    GENERIC_TA_CMD_GET_PROPERTY_BOOL = 2,
    GENERIC_TA_CMD_GET_PROPERTY_U32 = 3,
    GENERIC_TA_CMD_GET_PROPERTY_U64 = 4,
};


```

## 7. 内核服务层接口

### 7.1 属性服务处理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_property_request(struct property_request* req, struct property_response* resp)` |
| 输入 | req: 指向属性请求消息的指针，包含属性集、属性名称或索引<br>resp: 指向属性响应消息的指针，用于填充返回数据 |
| 输出 | resp: 填充属性查找结果，包括属性类型、名称、值和长度 |
| 返回值 | TEE_SUCCESS: 成功处理请求<br>TEE_ERROR_ITEM_NOT_FOUND: 属性未找到<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | TIPC服务端处理属性查找请求，在内核属性数据库中查找指定属性 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_property_enum_request(struct property_enum_request* req, struct property_enum_response* resp)` |
| 输入 | req: 指向属性枚举请求的指针，包含属性集、起始索引和请求数量<br>resp: 指向属性枚举响应的指针，用于填充返回数据 |
| 输出 | resp: 填充枚举结果，包括总数量、返回数量和属性列表 |
| 返回值 | TEE_SUCCESS: 成功处理枚举请求<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | TIPC服务端处理属性枚举请求，返回指定范围内的属性信息 |

### 7.2 内核属性管理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_client_properties(uint32_t client_id, const char* prop_name, struct property_response* resp)` |
| 输入 | client_id: 客户端标识符<br>prop_name: 属性名称字符串<br>resp: 指向属性响应消息的指针 |
| 输出 | resp: 填充客户端属性信息 |
| 返回值 | TEE_SUCCESS: 成功获取客户端属性<br>TEE_ERROR_ITEM_NOT_FOUND: 客户端属性未找到 |
| 说明 | 根据客户端ID动态生成客户端相关属性，如身份信息等 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result copy_property_to_response(const struct user_ta_property* prop, struct property_response* resp)` |
| 输入 | prop: 指向属性结构体的指针<br>resp: 指向属性响应消息的指针 |
| 输出 | resp: 填充属性信息到响应消息 |
| 返回值 | TEE_SUCCESS: 成功复制属性<br>TEE_ERROR_GENERIC: 不支持的属性类型 |
| 说明 | 将内核属性结构体的数据复制到TIPC响应消息中 |

### 7.3 属性数据库接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result enumerate_client_properties(struct property_enum_request* req, struct property_enum_response* resp)` |
| 输入 | req: 指向枚举请求的指针<br>resp: 指向枚举响应的指针 |
| 输出 | resp: 填充客户端属性枚举结果 |
| 返回值 | TEE_SUCCESS: 成功枚举客户端属性<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | 枚举客户端相关的动态属性，生成属性列表 |

## 8. 错误处理和工具接口

### 8.1 错误转换接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result sys_to_tee_error(int sys_error)` |
| 输入 | sys_error: 系统错误码(如TIPC错误码) |
| 输出 | 无 |
| 返回值 | 对应的TEE_Result错误码 |
| 说明 | 将系统级错误码转换为TEE标准错误码 |

### 8.2 内存管理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static void* property_malloc(size_t size)` |
| 输入 | size: 需要分配的内存大小 |
| 输出 | 无 |
| 返回值 | 成功: 指向分配内存的指针<br>失败: NULL |
| 说明 | 属性系统专用的内存分配函数，可能包含特殊的内存管理策略 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static void property_free(void* ptr)` |
| 输入 | ptr: 需要释放的内存指针 |
| 输出 | 无 |
| 返回值 | 无 |
| 说明 | 属性系统专用的内存释放函数，与property_malloc配对使用 |

### 8.3 字符串处理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static size_t property_strlcpy(char* dst, const char* src, size_t size)` |
| 输入 | dst: 目标缓冲区指针<br>src: 源字符串指针<br>size: 目标缓冲区大小 |
| 输出 | dst: 复制的字符串 |
| 返回值 | 源字符串的长度(不包括终止符) |
| 说明 | 安全的字符串复制函数，确保目标缓冲区不会溢出 |

## 9. 调试和日志接口

### 9.1 调试输出接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static void property_debug_print(const char* func, int line, const char* fmt, ...)` |
| 输入 | func: 函数名称字符串<br>line: 行号<br>fmt: 格式化字符串<br>...: 可变参数 |
| 输出 | 无 |
| 返回值 | 无 |
| 说明 | 属性系统专用的调试输出函数，可根据编译选项启用或禁用 |

### 9.2 性能统计接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static void property_perf_start(struct property_perf_ctx* ctx)` |
| 输入 | ctx: 指向性能统计上下文的指针 |
| 输出 | ctx: 初始化性能统计上下文 |
| 返回值 | 无 |
| 说明 | 开始性能统计，记录操作开始时间 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static void property_perf_end(struct property_perf_ctx* ctx, const char* operation)` |
| 输入 | ctx: 指向性能统计上下文的指针<br>operation: 操作名称字符串 |
| 输出 | 无 |
| 返回值 | 无 |
| 说明 | 结束性能统计，计算并记录操作耗时 |

## 10. 接口使用示例

### 10.1 属性获取示例

```c
/* 获取TEE实现属性示例 */
TEE_Result get_tee_version_example(void) {
    char version[64];
    size_t version_len = sizeof(version);
    TEE_Result res;

    res = TEE_GetPropertyAsString(TEE_PROPSET_TEE_IMPLEMENTATION,
                                  "gpd.tee.internalCore.version",
                                  version, &version_len);
    if (res == TEE_SUCCESS) {
        /* 使用version字符串 */
        property_debug_print(__func__, __LINE__,
                           "TEE version: %s", version);
    }

    return res;
}
```

### 10.2 属性枚举示例

```c
/* 枚举TEE属性示例 */
TEE_Result enumerate_tee_properties_example(void) {
    TEE_PropSetHandle enumerator;
    char prop_name[64];
    size_t name_len;
    TEE_Result res;

    /* 分配枚举器 */
    res = TEE_AllocatePropertyEnumerator(&enumerator);
    if (res != TEE_SUCCESS)
        return res;

    /* 启动枚举 */
    TEE_StartPropertyEnumerator(enumerator, TEE_PROPSET_TEE_IMPLEMENTATION);

    /* 遍历属性 */
    while (true) {
        name_len = sizeof(prop_name);
        res = TEE_GetPropertyName(enumerator, prop_name, &name_len);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        if (res != TEE_SUCCESS)
            goto cleanup;

        property_debug_print(__func__, __LINE__,
                           "Found property: %s", prop_name);

        res = TEE_GetNextProperty(enumerator);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        if (res != TEE_SUCCESS)
            goto cleanup;
    }

cleanup:
    TEE_FreePropertyEnumerator(enumerator);
    return TEE_SUCCESS;
}
```
